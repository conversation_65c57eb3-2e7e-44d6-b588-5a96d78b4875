# Test Cases cho Logic Gia hạn Khóa học

## Các trường hợp cần test:

### 1. <PERSON><PERSON><PERSON><PERSON> học chưa đăng ký
- **Input**: User chưa có enrollment
- **Expected**: Tạo đơn hàng mới bình thường

### 2. <PERSON><PERSON><PERSON><PERSON> học đang có hiệu lực (valid)
- **Input**: `enrollment_status == 'valid'`
- **Expected**: <PERSON><PERSON><PERSON> về thông báo "Bạn đã đăng ký khóa học này"

### 3. Kh<PERSON><PERSON> học hết hạn - chưa có đơn hàng
- **Input**: `enrollment_status == 'expired'`, không có đơn hàng nào
- **Expected**: Tạo đơn hàng mới để gia hạn

### 4. Kh<PERSON><PERSON> học hết hạn - c<PERSON> đơn hàng đã thanh toán
- **Input**: `enrollment_status == 'expired'`, có đơn hàng `status == 1`
- **Expected**: Bỏ qua đơn hàng cũ, tạ<PERSON> đơn hàng mới

### 5. <PERSON>hóa học hết hạn - có đơn hàng chưa thanh toán
- **Input**: `enrollment_status == 'expired'`, có đơn hàng `status == 0`
- **Expected**: Tạo đơn hàng mới (không cập nhật đơn cũ)

### 6. Khóa học còn hiệu lực - có đơn hàng chưa thanh toán
- **Input**: `enrollment_status == 'valid'`, có đơn hàng `status == 0`
- **Expected**: Cập nhật đơn hàng hiện có

## Test với Pricing Plans:

### 7. Gia hạn với pricing plan khác
- **Input**: Khóa học hết hạn, chọn pricing plan khác với lần mua trước
- **Expected**: Tạo đơn mới với pricing plan mới

### 8. Admin duyệt thanh toán gia hạn
- **Input**: Admin duyệt đơn hàng gia hạn
- **Expected**: Cập nhật enrollment hiện có, không tạo enrollment mới

## Kiểm tra thông báo tiếng Việt:

### 9. Các thông báo lỗi
- ✅ "Không tìm thấy khóa học"
- ✅ "Không tìm thấy email"
- ✅ "Mật khẩu không chính xác"
- ✅ "Bạn đã thanh toán cho khóa học này"

### 10. Các thông báo thành công
- ✅ "Thanh toán thành công"
- ✅ "Thanh toán sẽ được hoàn tất sau khi quản trị viên xem xét và phê duyệt"

## Cách test:

1. **Tạo khóa học test** với pricing plans
2. **Tạo user test**
3. **Tạo enrollment hết hạn** (set expiry_date < current time)
4. **Test các API endpoints**:
   - POST `/student/checkout-course`
   - POST `/student/check-payment-success-order`
5. **Test admin approval**:
   - GET `/admin/offline-payments/{id}/accept`

## Database Changes cần kiểm tra:

1. **offline_payments table**: Có tạo record mới khi gia hạn
2. **enrollments table**: Có cập nhật đúng expiry_date và pricing_plan_id
3. **coupons table**: Có giảm quantity đúng cách

## Expected Behavior Summary:

- **Khóa học hết hạn**: Luôn cho phép tạo đơn mới để gia hạn
- **Enrollment update**: Cập nhật enrollment hiện có thay vì tạo mới
- **Pricing plans**: Hỗ trợ đầy đủ trong cả trường hợp mới và gia hạn
- **Thông báo**: Tất cả đều bằng tiếng Việt, không dùng get_phrase
