# Test Cases cho Logic Gia hạn Khóa học - CẬP NHẬT

## ✅ CÁC VẤN ĐỀ ĐÃ ĐƯỢC SỬA:

### 🔧 **Vấn đề 1: T<PERSON>h thời gian gia hạn sai**
- **Trước**: T<PERSON>h từ ngày hết hạn cũ (5/7/2025 + 1 tháng = 5/8/2025)
- **Sau**: T<PERSON>h từ ngày hiện tại (hôm nay + 1 tháng)
- **Giải pháp**: Tạo function `calculateExpiryDateForRenewal()` riêng cho gia hạn

### 🔧 **Vấn đề 2: Enrollment không được cập nhật ngay**
- **Trước**: Chỉ cập nhật khi admin duyệt hoặc thanh toán miễn phí
- **Sau**: Cập nhật ngay khi tạo đơn gia hạn thành công
- **G<PERSON><PERSON>i pháp**: Thêm logic `$isRenewal` để cập nhật enrollment ngay lập tức

## 🆕 CÁC THAY ĐỔI CHÍNH:

### 1. **Course Model** - Thêm function mới:
```php
public function calculateExpiryDateForRenewal($planId)
{
    // Luôn tính từ ngày hiện tại cho gia hạn
    $days = $plan['duration_value'] * 30;
    return strtotime("+" . $days . " days");
}
```

### 2. **OfflinePaymentController** - Logic mới:
- ✅ Kiểm tra `$isRenewal = $existingEnrollment && enroll_status() == 'expired'`
- ✅ Cập nhật enrollment ngay khi `$total_amount == 0 || $isRenewal`
- ✅ Sử dụng `calculateExpiryDateForRenewal()` cho gia hạn

### 3. **Admin Controller** - Cập nhật logic duyệt:
- ✅ Kiểm tra gia hạn khi admin duyệt
- ✅ Sử dụng function mới cho tính thời gian gia hạn

### 4. **PaymentController** - Thanh toán tự động:
- ✅ Xử lý gia hạn cho thanh toán qua Sepay
- ✅ Cập nhật enrollment đúng cách

## 📋 TEST CASES CẬP NHẬT:

### ✅ **Test Case 1: Gia hạn khóa học hết hạn**
```
Input:
- User có enrollment với expiry_date = 5/7/2025 (đã hết hạn)
- Chọn gói 1 tháng
- Ngày hiện tại: 10/7/2025

Expected:
- Tạo đơn hàng mới thành công
- Enrollment được cập nhật: expiry_date = 10/8/2025 (từ ngày hiện tại)
- Có thể vào học ngay lập tức
```

### ✅ **Test Case 2: Kiểm tra thông báo tiếng Việt**
- ✅ "Không tìm thấy khóa học"
- ✅ "Bạn đã thanh toán cho khóa học này"
- ✅ "Thanh toán thành công"
- ✅ "Thanh toán thất bại! Vui lòng thử lại."

### ✅ **Test Case 3: Admin duyệt gia hạn**
```
Input: Admin duyệt đơn hàng gia hạn
Expected:
- Enrollment được cập nhật với thời gian mới
- Không tạo enrollment duplicate
- Thời gian tính từ ngày hiện tại
```

## 🔍 KIỂM TRA THỰC TẾ:

### 1. **Database Changes**:
- `enrollments.expiry_date` được cập nhật đúng
- `enrollments.pricing_plan_id` được cập nhật
- Không có duplicate enrollment

### 2. **User Experience**:
- Sau gia hạn có thể vào học ngay
- Thời gian hiển thị đúng (từ ngày hiện tại)
- Thông báo bằng tiếng Việt

### 3. **API Responses**:
- POST `/student/checkout-course` trả về đúng
- Enrollment status được cập nhật
- Player page cho phép truy cập

## 🎯 EXPECTED BEHAVIOR:

1. **Khóa học hết hạn**: ✅ Cho phép gia hạn
2. **Tính thời gian**: ✅ Từ ngày hiện tại
3. **Cập nhật ngay**: ✅ Không cần chờ admin duyệt
4. **Thông báo**: ✅ Tiếng Việt hoàn toàn
5. **Pricing plans**: ✅ Hỗ trợ đầy đủ
