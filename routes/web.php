<?php

use App\Http\Controllers\CommonController;
use App\Http\Controllers\InstallController;
use App\Http\Controllers\ModalController;
use App\Http\Controllers\PageController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\frontend\HomeController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\EmbedController;

//Cache clear route
Route::get('/clear-cache', function () {
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    Artisan::call('route:clear');
    Artisan::call('view:clear');
    Cache::flush();

    return 'Application cache cleared';
});

// Debug enrollment route
Route::get('/debug-enrollment/{course_slug}/{user_id?}', function($course_slug, $user_id = null) {

    // Lấy thông tin khóa học
    $course = \App\Models\Course::where('slug', $course_slug)->first();
    if (!$course) {
        return "❌ Không tìm thấy khóa học với slug: {$course_slug}";
    }

    // Lấy user_id hiện tại nếu không được cung cấp
    if (!$user_id) {
        $user_id = auth()->id();
    }

    if (!$user_id) {
        return "❌ Không có user_id";
    }

    echo "<h2>🔍 DEBUG ENROLLMENT STATUS</h2>";
    echo "<hr>";

    echo "<h3>📋 Thông tin cơ bản:</h3>";
    echo "• Course ID: {$course->id}<br>";
    echo "• Course Slug: {$course->slug}<br>";
    echo "• Course Title: {$course->title}<br>";
    echo "• User ID: {$user_id}<br>";
    echo "• Thời gian hiện tại: " . date('Y-m-d H:i:s') . " (" . time() . ")<br>";
    echo "<hr>";

    // Kiểm tra enrollment trong database
    echo "<h3>📊 Enrollment Records:</h3>";
    $enrollments = \App\Models\Enrollment::where('course_id', $course->id)
        ->where('user_id', $user_id)
        ->orderBy('id', 'desc')
        ->get();

    if ($enrollments->isEmpty()) {
        echo "❌ Không có enrollment nào được tìm thấy<br>";
    } else {
        echo "✅ Tìm thấy " . $enrollments->count() . " enrollment(s):<br><br>";

        foreach ($enrollments as $index => $enrollment) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Enrollment #" . ($index + 1) . " (ID: {$enrollment->id})</strong><br>";
            echo "• Entry Date: " . date('Y-m-d H:i:s', $enrollment->entry_date) . " (" . $enrollment->entry_date . ")<br>";
            echo "• Expiry Date: ";

            if ($enrollment->expiry_date) {
                $expiry_formatted = date('Y-m-d H:i:s', $enrollment->expiry_date);
                $is_expired = $enrollment->expiry_date < time();
                $status_icon = $is_expired ? "❌" : "✅";
                echo "{$expiry_formatted} ({$enrollment->expiry_date}) {$status_icon}<br>";

                if ($is_expired) {
                    $days_expired = floor((time() - $enrollment->expiry_date) / 86400);
                    echo "  → Đã hết hạn {$days_expired} ngày<br>";
                } else {
                    $days_remaining = floor(($enrollment->expiry_date - time()) / 86400);
                    echo "  → Còn lại {$days_remaining} ngày<br>";
                }
            } else {
                echo "NULL (Trọn đời) ✅<br>";
            }

            echo "• Enrollment Type: {$enrollment->enrollment_type}<br>";
            echo "• Pricing Plan ID: " . ($enrollment->pricing_plan_id ?? 'NULL') . "<br>";
            echo "• Paid Amount: " . ($enrollment->paid_amount ?? 'NULL') . "<br>";
            echo "• Created: {$enrollment->created_at}<br>";
            echo "• Updated: {$enrollment->updated_at}<br>";
            echo "</div>";
        }
    }

    echo "<hr>";

    // Test function enroll_status
    echo "<h3>🧪 Test enroll_status() function:</h3>";
    $enroll_status_result = enroll_status($course->id, $user_id);
    echo "• Kết quả enroll_status(): ";

    switch ($enroll_status_result) {
        case 'valid':
            echo "✅ <strong>VALID</strong> - Khóa học còn hiệu lực<br>";
            break;
        case 'expired':
            echo "❌ <strong>EXPIRED</strong> - Khóa học đã hết hạn<br>";
            break;
        case false:
            echo "⚠️ <strong>FALSE</strong> - Chưa đăng ký khóa học<br>";
            break;
        default:
            echo "❓ <strong>UNKNOWN</strong> - Kết quả không xác định: " . var_export($enroll_status_result, true) . "<br>";
    }

    echo "<hr>";
    echo "<h3>🔧 Khuyến nghị:</h3>";

    if ($enroll_status_result == 'expired') {
        echo "❌ <strong>VẤN ĐỀ:</strong> Function enroll_status() trả về 'expired'<br>";
        echo "🔍 <strong>NGUYÊN NHÂN CÓ THỂ:</strong><br>";
        echo "• Enrollment chưa được cập nhật sau khi gia hạn<br>";
        echo "• Có nhiều enrollment records và function lấy record cũ<br>";
        echo "• expiry_date chưa được cập nhật đúng<br><br>";
    } elseif ($enroll_status_result == 'valid') {
        echo "✅ <strong>TÌNH TRẠNG:</strong> Function enroll_status() hoạt động đúng<br>";
    }
})->middleware('auth');

// Route để dọn dẹp enrollment duplicates
Route::get('/cleanup-enrollments/{course_slug}/{user_id?}', function($course_slug, $user_id = null) {

    // Lấy thông tin khóa học
    $course = \App\Models\Course::where('slug', $course_slug)->first();
    if (!$course) {
        return "❌ Không tìm thấy khóa học với slug: {$course_slug}";
    }

    // Lấy user_id hiện tại nếu không được cung cấp
    if (!$user_id) {
        $user_id = auth()->id();
    }

    if (!$user_id) {
        return "❌ Không có user_id";
    }

    echo "<h2>🧹 CLEANUP ENROLLMENT DUPLICATES</h2>";
    echo "<hr>";

    // Lấy tất cả enrollments
    $enrollments = \App\Models\Enrollment::where('course_id', $course->id)
        ->where('user_id', $user_id)
        ->orderBy('id', 'desc')
        ->get();

    echo "<h3>📊 Trước khi dọn dẹp:</h3>";
    echo "• Tổng số enrollments: " . $enrollments->count() . "<br><br>";

    if ($enrollments->count() <= 1) {
        echo "✅ Không có duplicate enrollments<br>";
        return;
    }

    // Giữ lại enrollment mới nhất
    $latestEnrollment = $enrollments->first();
    $duplicateEnrollments = $enrollments->slice(1);

    echo "<h3>✅ Enrollment được giữ lại (mới nhất):</h3>";
    echo "• ID: {$latestEnrollment->id}<br>";
    echo "• Entry Date: " . date('Y-m-d H:i:s', $latestEnrollment->entry_date) . "<br>";
    echo "• Expiry Date: " . ($latestEnrollment->expiry_date ? date('Y-m-d H:i:s', $latestEnrollment->expiry_date) : 'NULL') . "<br>";
    echo "• Pricing Plan ID: " . ($latestEnrollment->pricing_plan_id ?? 'NULL') . "<br><br>";

    echo "<h3>🗑️ Enrollments sẽ bị xóa:</h3>";
    foreach ($duplicateEnrollments as $enrollment) {
        echo "• ID: {$enrollment->id} - Entry: " . date('Y-m-d H:i:s', $enrollment->entry_date) . "<br>";
    }

    // Xóa duplicates
    $deletedCount = 0;
    foreach ($duplicateEnrollments as $enrollment) {
        $enrollment->delete();
        $deletedCount++;
    }

    echo "<br><h3>✅ Kết quả:</h3>";
    echo "• Đã xóa {$deletedCount} enrollment duplicates<br>";
    echo "• Còn lại 1 enrollment (mới nhất)<br>";

    // Test lại enroll_status
    echo "<br><h3>🧪 Test enroll_status() sau khi dọn dẹp:</h3>";
    $enroll_status_result = enroll_status($course->id, $user_id);
    echo "• Kết quả: ";

    switch ($enroll_status_result) {
        case 'valid':
            echo "✅ <strong>VALID</strong> - Khóa học còn hiệu lực<br>";
            break;
        case 'expired':
            echo "❌ <strong>EXPIRED</strong> - Khóa học đã hết hạn<br>";
            break;
        case false:
            echo "⚠️ <strong>FALSE</strong> - Chưa đăng ký khóa học<br>";
            break;
    }

    echo "<br>🎉 <strong>Hoàn thành dọn dẹp!</strong><br>";
    echo "Bây giờ hãy thử truy cập lại khóa học để kiểm tra.";

})->middleware('auth');

Route::get('home/switch/{id}', [HomeController::class, 'homepage_switcher'])->name('home.switch');

//Redirect route
Route::get('/dashboard', function () {
    if (auth()->user()->role == 'admin') {
        return redirect(route('admin.dashboard'));
    }elseif(auth()->user()->role == 'student'){
        return redirect(route('my.courses'));
    } else {
        return redirect(route('home'));
    }
})->middleware(['auth', 'verified'])->name('dashboard');

//Common modal route
Route::get('modal/{view_path}', [ModalController::class, 'common_view_function'])->name('modal');
Route::any('get-video-details/{url?}', [CommonController::class, 'get_video_details'])->name('get.video.details');
Route::get('view/{path}', [CommonController::class, 'rendered_view'])->name('view');

Route::get('closed_back_to_mobile_ber', function () {
    session()->forget('app_url');
    return redirect()->back();
})->name('closed_back_to_mobile_ber');

//Mobile payment redirect
Route::get('payment/web_redirect_to_pay_fee', [PaymentController::class, 'webRedirectToPayFee'])->name('payment.web_redirect_to_pay_fee');


// Account inactive route
Route::get('/account-inactive', function () {
    return view('auth.account-inactive');
})->name('account.inactive');
//Installation routes
Route::get('/violation',function (){
    return view('errors.451');
})->name('violation');

// Device limitation route
Route::get('/device-limitation',function (){
    return view('errors.421');
})->name('device.limitation');

// Route để lấy popup ngẫu nhiên cho video player
Route::get('/get-random-popup', [App\Http\Controllers\Admin\MarketingController::class, 'getRandomPopup'])->name('get.random.popup');

// API routes cho pricing plans
Route::prefix('api/pricing-plans')->group(function() {
    Route::get('/course/{courseId}', [App\Http\Controllers\Api\PricingPlanController::class, 'getCoursePricingPlans'])->name('api.pricing.plans.course');
    Route::get('/course/{courseId}/plan/{planId}', [App\Http\Controllers\Api\PricingPlanController::class, 'getPricingPlanDetails'])->name('api.pricing.plan.details');
    Route::post('/course/{courseId}/plan/{planId}/calculate', [App\Http\Controllers\Api\PricingPlanController::class, 'calculatePlanPrice'])->name('api.pricing.plan.calculate');
});

// CRM Routes
Route::group(['middleware' => ['auth', 'verified'], 'prefix' => 'admin'], function() {
    // API routes cho CRM
    Route::post('/crm/activities', [App\Http\Controllers\CrmController::class, 'storeActivity'])->name('admin.crm.store.activity');
    Route::get('/crm/activities/{userId}', [App\Http\Controllers\CrmController::class, 'getActivities'])->name('admin.crm.get.activities');
    Route::post('/crm/opportunities', [App\Http\Controllers\CrmController::class, 'storeOpportunity'])->name('admin.crm.store.opportunity');
    Route::get('/crm/opportunities/{userId}', [App\Http\Controllers\CrmController::class, 'getOpportunities'])->name('admin.crm.get.opportunities');
    Route::match(['put', 'post'], '/crm/opportunities/{id}/status', [App\Http\Controllers\CrmController::class, 'updateOpportunityStatus'])->name('admin.crm.update.opportunity.status');

    // Report routes
    Route::get('/crm/performance', [App\Http\Controllers\CrmController::class, 'salesPerformance'])->name('admin.crm.performance');
    Route::get('/crm/funnel', [App\Http\Controllers\CrmController::class, 'salesFunnel'])->name('admin.crm.funnel');
});

// Embeddable Registration Form
Route::middleware(['cors'])->group(function() {
    Route::get('/embed/register/{course_slug}', [EmbedController::class, 'showEmbedRegistrationForm'])
        ->name('embed.registration')
        ->middleware('allow-embedding');
    Route::post('/embed/register/{course_slug}', [EmbedController::class, 'processEmbedRegistration'])->name('embed.register.process');
});

// Page routes - must be at the end to avoid conflicts
Route::get('/{slug}', [PageController::class, 'show'])->name('page.show')->where('slug', '[a-zA-Z0-9\-_]+');
