@extends('layouts.admin')
@push('title', '<PERSON><PERSON><PERSON> ký học viên')
@push('meta')@endpush
@push('css')
<style>
.enrollment-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e8ecef;
}
.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
.duration-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}
.duration-option:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.duration-option.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}
.duration-option input[type="radio"] {
    display: none;
}
.duration-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}
.duration-desc {
    font-size: 12px;
    color: #6c757d;
}
</style>
@endpush
@section('content')
    @php
        $course = App\Models\Course::where('status', 'active')->orWhere('status', 'private')->orderBy('title', 'asc')->get();
    @endphp

    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-4 px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-user-add me-2"></i>
                    Đăng ký học viên vào khóa học
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="ol-card p-4">
                <h3 class="title fs-14px mb-3">{{get_phrase('Enroll students')}}</h3>
                <div class="ol-card-body">
                    <form class="" action="{{ route('admin.student.post') }}" method="post" enctype="multipart/form-data">
                        @csrf

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="course_id">{{ get_phrase('Course to enrol') }}<span class="text-danger ms-1">*</span></label>

                            <select class="ol-select2 form-control" data-toggle="select2" name="course_id"
                                id="course_id" data-placeholder="Choose ..." required onchange="fetchAvailableStudents()">
                                <option value="">{{ get_phrase('Select a course') }}</option>
                                @foreach ($course as $row)
                                    <option value="{{ $row->id }}">{{ $row->title }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="multiple_user_id">{{ get_phrase('Available Students') }}<span class="required text-danger">*</span>
                            </label>
                            <select class="ol-select2 select2-hidden-accessible" name="user_id[]" id="multiple_user_id" multiple="multiple" required>
                                <option value="">{{ get_phrase('Please select a course first') }}</option>
                            </select>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="amount_received">Số tiền đã thu<span class="text-danger ms-1">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control ol-form-control" name="amount_received" id="amount_received"
                                       placeholder="0" min="0" step="1000" required>
                                <span class="input-group-text">VNĐ</span>
                            </div>
                            <small class="text-muted">Nhập số tiền đã thu từ khách hàng</small>
                        </div>

                        <button type="submit" class="btn ol-btn-primary mt-2">{{ get_phrase('Enroll student') }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        function fetchAvailableStudents() {
            var courseId = $('#course_id').val();
            if (!courseId) {
                $('#multiple_user_id').html('<option value="">{{ get_phrase("Please select a course first") }}</option>');
                return;
            }

            $.ajax({
                url: "{{ route('admin.available.students') }}",
                type: "POST",
                data: {
                    course_ids: [courseId],
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    $('#multiple_user_id').html('');
                    if (response.students.length > 0) {
                        $.each(response.students, function(key, student) {
                            $('#multiple_user_id').append('<option value="' + student.id + '">' + student.name + ' (' + student.email + ')</option>');
                        });
                    } else {
                        $('#multiple_user_id').html('<option value="">{{ get_phrase("No available students found") }}</option>');
                    }
                    // Initialize or refresh Select2
                    $('.ol-select2').select2();
                }
            });
        }

        // Format number input for amount
        $(document).ready(function() {
            $('#amount_received').on('input', function() {
                let value = $(this).val().replace(/[^0-9]/g, '');
                if (value) {
                    // Format with thousand separators
                    let formatted = new Intl.NumberFormat('vi-VN').format(value);
                    // Update placeholder to show formatted value
                    $(this).attr('title', formatted + ' VNĐ');
                }
            });

            // Form validation
            $('form').on('submit', function(e) {
                let amount = $('#amount_received').val();
                if (!amount || amount <= 0) {
                    e.preventDefault();
                    alert('Vui lòng nhập số tiền đã thu hợp lệ');
                    $('#amount_received').focus();
                    return false;
                }
            });
        });
    </script>
@endsection
