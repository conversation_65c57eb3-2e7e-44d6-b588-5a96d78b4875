<?php
/**
 * Debug script để kiểm tra enrollment status
 * Chạy script này để debug vấn đề enrollment
 */

// Thêm vào routes/web.php để test:
// Route::get('/debug-enrollment/{course_slug}/{user_id?}', function($course_slug, $user_id = null) {

use App\Models\Course;
use App\Models\Enrollment;
use Carbon\Carbon;

function debugEnrollment($course_slug, $user_id = null) {
    // Lấy thông tin khóa học
    $course = Course::where('slug', $course_slug)->first();
    if (!$course) {
        return "❌ Không tìm thấy khóa học với slug: {$course_slug}";
    }

    // Lấy user_id hiện tại nếu không được cung cấp
    if (!$user_id) {
        $user_id = auth()->id();
    }

    if (!$user_id) {
        return "❌ Không có user_id";
    }

    echo "<h2>🔍 DEBUG ENROLLMENT STATUS</h2>";
    echo "<hr>";
    
    echo "<h3>📋 Thông tin cơ bản:</h3>";
    echo "• Course ID: {$course->id}<br>";
    echo "• Course Slug: {$course->slug}<br>";
    echo "• Course Title: {$course->title}<br>";
    echo "• User ID: {$user_id}<br>";
    echo "• Thời gian hiện tại: " . date('Y-m-d H:i:s') . " (" . time() . ")<br>";
    echo "<hr>";

    // Kiểm tra enrollment trong database
    echo "<h3>📊 Enrollment Records:</h3>";
    $enrollments = Enrollment::where('course_id', $course->id)
        ->where('user_id', $user_id)
        ->orderBy('id', 'desc')
        ->get();

    if ($enrollments->isEmpty()) {
        echo "❌ Không có enrollment nào được tìm thấy<br>";
    } else {
        echo "✅ Tìm thấy " . $enrollments->count() . " enrollment(s):<br><br>";
        
        foreach ($enrollments as $index => $enrollment) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Enrollment #" . ($index + 1) . " (ID: {$enrollment->id})</strong><br>";
            echo "• Entry Date: " . date('Y-m-d H:i:s', $enrollment->entry_date) . " (" . $enrollment->entry_date . ")<br>";
            echo "• Expiry Date: ";
            
            if ($enrollment->expiry_date) {
                $expiry_formatted = date('Y-m-d H:i:s', $enrollment->expiry_date);
                $is_expired = $enrollment->expiry_date < time();
                $status_icon = $is_expired ? "❌" : "✅";
                echo "{$expiry_formatted} ({$enrollment->expiry_date}) {$status_icon}<br>";
                
                if ($is_expired) {
                    $days_expired = floor((time() - $enrollment->expiry_date) / 86400);
                    echo "  → Đã hết hạn {$days_expired} ngày<br>";
                } else {
                    $days_remaining = floor(($enrollment->expiry_date - time()) / 86400);
                    echo "  → Còn lại {$days_remaining} ngày<br>";
                }
            } else {
                echo "NULL (Trọn đời) ✅<br>";
            }
            
            echo "• Enrollment Type: {$enrollment->enrollment_type}<br>";
            echo "• Pricing Plan ID: " . ($enrollment->pricing_plan_id ?? 'NULL') . "<br>";
            echo "• Paid Amount: " . ($enrollment->paid_amount ?? 'NULL') . "<br>";
            echo "• Created: {$enrollment->created_at}<br>";
            echo "• Updated: {$enrollment->updated_at}<br>";
            echo "</div>";
        }
    }
    
    echo "<hr>";

    // Test function enroll_status
    echo "<h3>🧪 Test enroll_status() function:</h3>";
    $enroll_status_result = enroll_status($course->id, $user_id);
    echo "• Kết quả enroll_status(): ";
    
    switch ($enroll_status_result) {
        case 'valid':
            echo "✅ <strong>VALID</strong> - Khóa học còn hiệu lực<br>";
            break;
        case 'expired':
            echo "❌ <strong>EXPIRED</strong> - Khóa học đã hết hạn<br>";
            break;
        case false:
            echo "⚠️ <strong>FALSE</strong> - Chưa đăng ký khóa học<br>";
            break;
        default:
            echo "❓ <strong>UNKNOWN</strong> - Kết quả không xác định: " . var_export($enroll_status_result, true) . "<br>";
    }
    
    echo "<hr>";

    // Kiểm tra offline payments
    echo "<h3>💳 Offline Payments:</h3>";
    $payments = \App\Models\OfflinePayment::where('course_id', $course->id)
        ->where('user_id', $user_id)
        ->orderBy('id', 'desc')
        ->get();

    if ($payments->isEmpty()) {
        echo "❌ Không có payment nào được tìm thấy<br>";
    } else {
        echo "✅ Tìm thấy " . $payments->count() . " payment(s):<br><br>";
        
        foreach ($payments as $index => $payment) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Payment #" . ($index + 1) . " (ID: {$payment->id})</strong><br>";
            echo "• Status: ";
            switch ($payment->status) {
                case 0:
                    echo "⏳ Chờ thanh toán<br>";
                    break;
                case 1:
                    echo "✅ Đã thanh toán<br>";
                    break;
                case 2:
                    echo "❌ Đã hủy<br>";
                    break;
                default:
                    echo "❓ Không xác định ({$payment->status})<br>";
            }
            echo "• Total Amount: {$payment->total_amount}<br>";
            echo "• Pricing Plan ID: " . ($payment->pricing_plan_id ?? 'NULL') . "<br>";
            echo "• Transaction Content: " . ($payment->transaction_content ?? 'NULL') . "<br>";
            echo "• Created: {$payment->created_at}<br>";
            echo "• Updated: {$payment->updated_at}<br>";
            echo "</div>";
        }
    }

    echo "<hr>";
    echo "<h3>🔧 Khuyến nghị:</h3>";
    
    if ($enroll_status_result == 'expired') {
        echo "❌ <strong>VẤN ĐỀ:</strong> Function enroll_status() trả về 'expired'<br>";
        echo "🔍 <strong>NGUYÊN NHÂN CÓ THỂ:</strong><br>";
        echo "• Enrollment chưa được cập nhật sau khi gia hạn<br>";
        echo "• Có nhiều enrollment records và function lấy record cũ<br>";
        echo "• expiry_date chưa được cập nhật đúng<br><br>";
        
        echo "💡 <strong>GIẢI PHÁP:</strong><br>";
        echo "• Kiểm tra enrollment mới nhất có expiry_date đúng không<br>";
        echo "• Cập nhật lại expiry_date nếu cần<br>";
        echo "• Xóa các enrollment cũ nếu có duplicate<br>";
    } elseif ($enroll_status_result == 'valid') {
        echo "✅ <strong>TÌNH TRẠNG:</strong> Function enroll_status() hoạt động đúng<br>";
        echo "🔍 <strong>VẤN ĐỀ CÓ THỂ:</strong><br>";
        echo "• Cache trong PlayerController<br>";
        echo "• Logic khác trong PlayerController<br>";
        echo "• Session hoặc middleware issue<br>";
    }
}

// Để sử dụng, thêm route này vào routes/web.php:
/*
Route::get('/debug-enrollment/{course_slug}/{user_id?}', function($course_slug, $user_id = null) {
    debugEnrollment($course_slug, $user_id);
})->middleware('auth');
*/
